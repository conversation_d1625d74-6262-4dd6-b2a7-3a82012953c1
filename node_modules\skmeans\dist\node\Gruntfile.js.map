{"version": 3, "sources": ["../../Gruntfile.js"], "names": ["module", "exports", "grunt", "require", "initConfig", "pkg", "file", "readJSON", "browserify", "dist", "watch", "keepAlive", "files", "babel", "options", "sourceMap", "presets", "expand", "src", "dest", "ext", "uglify", "banner", "clean", "registerTask"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB,UAASC,KAAT,EAAgB;AAChCC,SAAQ,kBAAR,EAA4BD,KAA5B;;AAEA;AACAA,OAAME,UAAN,CAAiB;AACfC,OAAKH,MAAMI,IAAN,CAAWC,QAAX,CAAoB,cAApB,CADU;AAEhBC,cAAY;AACXC,SAAM;AACLC,WAAO,IADF;AAELC,eAAW,IAFN;AAGLC,WAAO;AACN,gCAA2B,CAAC,YAAD;AADrB;AAHF;AADK,GAFI;AAWhBC,SAAO;AACNC,YAAS;AACRC,eAAW,IADH;AAERC,aAAS,CAAC,QAAD;AAFD,IADH;AAKNP,SAAM;AACLG,WAAO,CACN;AACCK,aAAQ,IADT;AAECC,UAAK,CAAC,MAAD,CAFN;AAGCC,WAAM,WAHP;AAICC,UAAK;AAJN,KADM,EAON;AACC,gCAA2B,CAAC,yBAAD;AAD5B,KAPM;AADF;AALA,GAXS;AA8BfC,UAAQ;AACNP,YAAS;AACPQ,YAAQ;AADD,IADH;AAIRb,SAAO;AACNG,WAAO;AACN,oCAAgC,CAAC,yBAAD;AAD1B;AADD;AAJC,GA9BO;AAwChBW,SAAO,CAAC,mBAAD,EAAqB,oBAArB;AAxCS,EAAjB;;AA2CArB,OAAMsB,YAAN,CAAmB,SAAnB,EAA8B,CAAC,YAAD,EAAc,OAAd,EAAsB,QAAtB,CAA9B;AACA,CAhDD", "file": "Gruntfile.js", "sourcesContent": ["module.exports = function(grunt) {\n\trequire('load-grunt-tasks')(grunt);\n\n\t// Project configuration.\n\tgrunt.initConfig({\n\t  pkg: grunt.file.readJSON('package.json'),\n\t\tbrowserify: {\n\t\t\tdist: {\n\t\t\t\twatch: true,\n\t\t\t\tkeepAlive: true,\n\t\t\t\tfiles: {\n\t\t\t\t\t'dist/browser/skmeans.js': ['browser.js']\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tbabel: {\n\t\t\toptions: {\n\t\t\t\tsourceMap: true,\n\t\t\t\tpresets: ['es2015']\n\t\t\t},\n\t\t\tdist: {\n\t\t\t\tfiles: [\n\t\t\t\t\t{\n\t\t\t\t\t\texpand: true,\n\t\t\t\t\t\tsrc: ['*.js'],\n\t\t\t\t\t\tdest: 'dist/node',\n\t\t\t\t\t\text: '.js'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\t'dist/browser/skmeans.js': ['dist/browser/skmeans.js'],\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\t  uglify: {\n\t    options: {\n\t      banner: '/*! <%= pkg.name %> <%= grunt.template.today(\"yyyy-mm-dd\") %> */\\n'\n\t    },\n\t\t\tdist : {\n\t\t\t\tfiles: {\n\t\t\t\t\t'dist/browser/skmeans.min.js' : ['dist/browser/skmeans.js']\n\t\t\t\t}\n\t\t\t}\n\t  },\n\t\tclean: ['dist/browser/*.js','dist/browser/*.map']\n\t});\n\n\tgrunt.registerTask('default', ['browserify','babel','uglify']);\n};\n"]}