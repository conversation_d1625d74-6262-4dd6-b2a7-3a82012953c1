{"name": "<PERSON><PERSON><PERSON><PERSON>-client", "version": "3.1.0", "description": "Manipulate TopoJSON and convert it to GeoJSON.", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "topology", "g<PERSON><PERSON><PERSON>"], "homepage": "https://github.com/topojson/topojson-client", "license": "ISC", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "main": "dist/topojson-client.js", "unpkg": "dist/topojson-client.min.js", "jsdelivr": "dist/topojson-client.min.js", "module": "src/index.js", "repository": {"type": "git", "url": "https://github.com/topojson/topojson-client.git"}, "bin": {"topo2geo": "bin/topo2geo", "topomerge": "bin/topomerge", "topoquantize": "bin/topoquantize"}, "files": ["bin/topo*", "dist/**/*.js", "src/**/*.js"], "scripts": {"pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src test", "prepublishOnly": "rm -rf dist && yarn test", "postpublish": "git push && git push --tags && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js"}, "dependencies": {"commander": "2"}, "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}}