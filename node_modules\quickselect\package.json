{"name": "quickselect", "version": "2.0.0", "description": "A tiny and fast selection algorithm in JavaScript.", "module": "index.js", "main": "quickselect.js", "dependencies": {}, "devDependencies": {"eslint": "^4.19.1", "eslint-config-mourner": "^2.0.3", "esm": "^3.0.15", "rollup": "^0.57.1", "tape": "^4.9.0"}, "eslintConfig": {"extends": "mourner", "parserOptions": {"sourceType": "module"}}, "scripts": {"pretest": "eslint index.js test.js bench.js", "test": "node -r esm test.js", "bench": "node -r esm bench.js", "build": "rollup -c", "prepublishOnly": "npm run build"}, "files": ["index.js", "quickselect.js"], "keywords": ["selection", "algorithm", "quickselect", "sort", "partial", "floyd", "rivest"], "author": "<PERSON>", "license": "ISC"}