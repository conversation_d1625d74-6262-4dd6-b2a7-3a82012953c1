{"version": 3, "sources": ["../../distance.js"], "names": ["module", "exports", "eudist", "v1", "v2", "sqrt", "len", "length", "sum", "i", "d", "Math", "mandist", "abs", "dist"], "mappings": ";;AAAAA,OAAOC,OAAP,GAAiB;AAChB;;;AAGAC,OAJgB,kBAITC,EAJS,EAINC,EAJM,EAIHC,IAJG,EAIG;AAClB,MAAIC,MAAMH,GAAGI,MAAb;AACA,MAAIC,MAAM,CAAV;;AAEA,OAAI,IAAIC,IAAE,CAAV,EAAYA,IAAEH,GAAd,EAAkBG,GAAlB,EAAuB;AACtB,OAAIC,IAAI,CAACP,GAAGM,CAAH,KAAO,CAAR,KAAcL,GAAGK,CAAH,KAAO,CAArB,CAAR;AACAD,UAAOE,IAAEA,CAAT;AACA;AACD;AACA,SAAOL,OAAMM,KAAKN,IAAL,CAAUG,GAAV,CAAN,GAAuBA,GAA9B;AACA,EAde;AAgBhBI,QAhBgB,mBAgBRT,EAhBQ,EAgBLC,EAhBK,EAgBFC,IAhBE,EAgBI;AACnB,MAAIC,MAAMH,GAAGI,MAAb;AACA,MAAIC,MAAM,CAAV;;AAEA,OAAI,IAAIC,IAAE,CAAV,EAAYA,IAAEH,GAAd,EAAkBG,GAAlB,EAAuB;AACtBD,UAAOG,KAAKE,GAAL,CAAS,CAACV,GAAGM,CAAH,KAAO,CAAR,KAAcL,GAAGK,CAAH,KAAO,CAArB,CAAT,CAAP;AACA;;AAED;AACA,SAAOJ,OAAMM,KAAKN,IAAL,CAAUG,GAAV,CAAN,GAAuBA,GAA9B;AACA,EA1Be;;;AA4BhB;;;AAGAM,KA/BgB,gBA+BXX,EA/BW,EA+BRC,EA/BQ,EA+BLC,IA/BK,EA+BC;AAChB,MAAIK,IAAIC,KAAKE,GAAL,CAASV,KAAGC,EAAZ,CAAR;AACA,SAAOC,OAAMK,CAAN,GAAUA,IAAEA,CAAnB;AACA;AAlCe,CAAjB", "file": "distance.js", "sourcesContent": ["module.exports = {\n\t/**\n\t * Euclidean distance\n\t */\n\teudist(v1,v2,sqrt) {\n\t\tvar len = v1.length;\n\t\tvar sum = 0;\n\n\t\tfor(let i=0;i<len;i++) {\n\t\t\tvar d = (v1[i]||0) - (v2[i]||0);\n\t\t\tsum += d*d;\n\t\t}\n\t\t// Square root not really needed\n\t\treturn sqrt? Math.sqrt(sum) : sum;\n\t},\n\n\tmandist(v1,v2,sqrt) {\n\t\tvar len = v1.length;\n\t\tvar sum = 0;\n\n\t\tfor(let i=0;i<len;i++) {\n\t\t\tsum += Math.abs((v1[i]||0) - (v2[i]||0));\n\t\t}\n\n\t\t// Square root not really needed\n\t\treturn sqrt? Math.sqrt(sum) : sum;\n\t},\n\n\t/**\n\t * Unidimensional distance\n\t */\n\tdist(v1,v2,sqrt) {\n\t\tvar d = Math.abs(v1-v2);\n\t\treturn sqrt? d : d*d;\n\t}\n\n}\n"]}