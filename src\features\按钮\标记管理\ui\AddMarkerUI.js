/**
 * 标记点管理界面组件
 * 提供标记点管理的用户界面及交互
 */
class AddMarkerUI {
    /**
     * 初始化AddMarkerUI实例
     * @param {Object} viewer - Cesium viewer实例
     * @param {String} containerId - 工具按钮容器ID
     * @returns {AddMarkerUI} UI实例
     */
    static init(viewer, containerId) {
        const instance = new AddMarkerUI(viewer, containerId);
        return instance;
    }
    
    /**
     * 构造函数
     * @param {Object} viewer - Cesium viewer实例
     * @param {String} containerId - 工具按钮容器ID
     */
    constructor(viewer, containerId) {
        this.viewer = viewer;
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.error(`未找到ID为 ${containerId} 的容器元素`);
            return;
        }
        
        // 创建工具实例
        this.tool = new AddMarkerTool(viewer, {
            markerImageUrl: 'src/features/按钮/标记管理/images/marker.svg',
            editable: true,
            useServerStorage: false
        });
        
        // 添加工具按钮
        this._addToolButton();
        
        // 创建面板但不显示
        this._createPanel();
        
        // 创建编辑窗口但不显示
        this._createEditPopup();
        
        // 保存全局实例，用于事件回调
        window.addMarkerUI = this;
        
        // 状态变量
        this.isPanelVisible = false;
        this.isEditPopupVisible = false;
        this.isActive = false;
        
        // 绑定事件处理函数
        this._bindEvents();
    }
    
    /**
     * 添加工具按钮到工具栏
     * @private
     */
    _addToolButton() {
        // 创建按钮元素
        const button = document.createElement('button');
        button.title = '标记管理';
        
        // 创建SVG图标引用
        button.innerHTML = `
            <svg width="24" height="24">
                <use xlink:href="#icon-marker"></use>
            </svg>
            <div class="tooltip">标记管理</div>
        `;
        
        // 添加点击事件
        button.addEventListener('click', () => {
            this.togglePanel();
        });
        
        // 保存按钮引用
        this.toolButton = button;
        
        // 添加到容器
        this.container.appendChild(button);
    }
    
    /**
     * 创建标记管理面板
     * @private
     */
    _createPanel() {
        // 创建面板容器
        const panel = document.createElement('div');
        panel.className = 'addmarker-panel';
        panel.style.display = 'none';
        
        // 设置面板位置 (固定位置)
        panel.style.left = '20px';
        panel.style.top = '100px';
        panel.style.transform = 'none';
        
        // 面板内容
        panel.innerHTML = `
            <div class="addmarker-panel-header">
                <ul>
                    <li><button id="btn_addmarker_add" title="添加标记"><i class="fas fa-map-marker-alt"></i></button></li>
                    <li><button id="btn_addmarker_openfile" title="打开文件"><i class="fas fa-folder-open"></i></button></li>
                    <li><button id="btn_addmarker_savefile" title="保存文件"><i class="fas fa-save"></i></button></li>
                    <li><button id="btn_addmarker_delall" title="清空标记"><i class="fas fa-trash"></i></button></li>
                    <li><button id="btn_addmarker_isedit" title="编辑模式"><i class="fas fa-unlock"></i></button></li>
                </ul>
                <span style="position: absolute; right: 10px; top: 0;">
                    <button id="btn_addmarker_close" style="background: none; border: none; color: #888; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </span>
            </div>
            <div class="addmarker-panel-body">
                <table id="addmarker_table" class="addmarker-table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th style="width: 60px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="addmarker_tbody"></tbody>
                </table>
            </div>
            <input id="addmarker_file_input" type="file" accept=".json" style="display:none;" />
        `;
        
        // 添加到document
        document.body.appendChild(panel);
        
        // 保存面板引用
        this.panel = panel;
    }
    
    /**
     * 创建编辑弹窗
     * @private
     */
    _createEditPopup() {
        // 创建弹窗容器
        const popup = document.createElement('div');
        popup.className = 'addmarker-popup';
        popup.style.display = 'none';
        popup.style.position = 'absolute';
        popup.style.zIndex = '1000';
        popup.style.backgroundColor = 'white';
        popup.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        popup.style.borderRadius = '5px';
        popup.style.width = '280px';
        
        // 弹窗内容
        popup.innerHTML = `
            <div class="addmarker-popup-titile">编辑标记</div>
            <div class="addmarker-popup-content">
                <div class="form-group">
                    <label for="addmarker_attr_name">名称</label>
                    <input type="text" id="addmarker_attr_name" class="form-control" placeholder="请输入标记名称" />
                </div>
                <div class="form-group">
                    <label for="addmarker_attr_remark">备注</label>
                    <textarea id="addmarker_attr_remark" class="form-control" rows="3" style="resize: none;" placeholder="请输入备注（可选填）"></textarea>
                </div>
                <div class="form-group" style="text-align: center;">
                    <button id="addmarker_save_btn" class="btn btn-primary btn-sm">保存</button>
                    &nbsp;&nbsp;
                    <button id="addmarker_delete_btn" class="btn btn-danger btn-sm">删除</button>
                </div>
            </div>
        `;
        
        // 添加到document
        document.body.appendChild(popup);
        
        // 保存弹窗引用
        this.editPopup = popup;
        
        // 保存当前编辑的标记ID
        this.currentEditMarkerId = null;
    }
    
    /**
     * 绑定事件处理函数
     * @private
     */
    _bindEvents() {
        // 面板按钮事件
        document.getElementById('btn_addmarker_add').addEventListener('click', () => {
            this.tool.startDraw();
        });
        
        document.getElementById('btn_addmarker_delall').addEventListener('click', () => {
            if (confirm('确定要清空所有标记吗？')) {
                this.tool.clearMarkers();
                this.updateMarkerList();
            }
        });
        
        // 编辑模式切换
        const editButton = document.getElementById('btn_addmarker_isedit');
        let isEditMode = true;
        
        editButton.addEventListener('click', () => {
            isEditMode = !isEditMode;
            
            if (isEditMode) {
                editButton.classList.remove('active');
                editButton.querySelector('i').classList.remove('fa-lock');
                editButton.querySelector('i').classList.add('fa-unlock');
            } else {
                editButton.classList.add('active');
                editButton.querySelector('i').classList.remove('fa-unlock');
                editButton.querySelector('i').classList.add('fa-lock');
            }
            
            this.tool.setEditable(isEditMode);
        });
        
        // 关闭面板按钮
        document.getElementById('btn_addmarker_close').addEventListener('click', () => {
            this.hidePanel();
        });
        
        // 文件操作
        const fileInput = document.getElementById('addmarker_file_input');
        
        document.getElementById('btn_addmarker_openfile').addEventListener('click', () => {
            fileInput.onchange = (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                const fileName = file.name;
                const fileType = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
                
                if (fileType !== 'json') {
                    alert('文件类型不合法，请选择JSON格式标注文件！');
                    this._clearFileInput();
                    return;
                }
                
                // 读取文件内容
                const reader = new FileReader();
                reader.readAsText(file, 'UTF-8');
                reader.onloadend = () => {
                    try {
                        const data = JSON.parse(reader.result);
                        this.tool.importFromJson(data, true);
                        this.updateMarkerList();
                    } catch (error) {
                        console.error('解析JSON文件失败:', error);
                        alert('文件格式错误，无法导入标记数据！');
                    }
                    this._clearFileInput();
                };
            };
            fileInput.click();
        });
        
        document.getElementById('btn_addmarker_savefile').addEventListener('click', () => {
            const jsonData = this.tool.exportToJson();
            
            if (jsonData === '[]') {
                alert('当前未标记任何点！');
                return;
            }
            
            // 创建下载链接
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '标记点数据.json';
            a.click();
            
            // 释放URL对象
            setTimeout(() => {
                URL.revokeObjectURL(url);
            }, 100);
        });
        
        // 编辑弹窗按钮事件
        document.getElementById('addmarker_save_btn').addEventListener('click', () => {
            this.saveCurrentMarker();
        });
        
        document.getElementById('addmarker_delete_btn').addEventListener('click', () => {
            if (this.currentEditMarkerId) {
                const marker = this.tool.getMarkerById(this.currentEditMarkerId);
                if (marker) {
                    const markerData = marker.properties.markerData.getValue();
                    const name = markerData.name || '未命名标记';
                    
                    if (confirm(`确定要删除标记 "${name}" 吗？`)) {
                        this.tool.deleteMarker(this.currentEditMarkerId);
                        this.hideEditPopup();
                    }
                }
            }
        });
        
        // 监听标记工具事件
        this.tool.on('markersUpdated', () => {
            this.updateMarkerList();
        });
        
        this.tool.on('markerSelected', (marker) => {
            if (marker && marker.properties && marker.properties.markerData) {
                const markerData = marker.properties.markerData.getValue();
                this.showEditPopup(marker.id, markerData);
            }
        });
        
        this.tool.on('markerDeselected', () => {
            this.hideEditPopup();
        });
    }
    
    /**
     * 显示编辑弹窗
     * @param {String} markerId - 标记ID
     * @param {Object} markerData - 标记数据
     */
    showEditPopup(markerId, markerData) {
        if (!this.editPopup) return;
        
        // 设置当前编辑的标记ID
        this.currentEditMarkerId = markerId;
        
        // 设置表单值
        document.getElementById('addmarker_attr_name').value = markerData.name || '';
        document.getElementById('addmarker_attr_remark').value = markerData.remark || '';
        
        // 获取标记在屏幕上的位置
        const marker = this.tool.getMarkerById(markerId);
        if (marker && marker.position) {
            const position = marker.position.getValue(Cesium.JulianDate.now());
            const canvasPosition = this.viewer.scene.cartesianToCanvasCoordinates(position);
            
            if (canvasPosition) {
                // 设置弹窗位置 (标记点上方)
                this.editPopup.style.left = canvasPosition.x - 140 + 'px'; // 居中显示
                this.editPopup.style.top = canvasPosition.y - 200 + 'px'; // 在标记上方
            }
        }
        
        // 显示弹窗
        this.editPopup.style.display = 'block';
        this.isEditPopupVisible = true;
    }
    
    /**
     * 隐藏编辑弹窗
     */
    hideEditPopup() {
        if (this.editPopup) {
            this.editPopup.style.display = 'none';
            this.isEditPopupVisible = false;
            this.currentEditMarkerId = null;
        }
    }
    
    /**
     * 保存当前标记
     */
    saveCurrentMarker() {
        if (!this.currentEditMarkerId) return;
        
        const name = document.getElementById('addmarker_attr_name').value.trim();
        const remark = document.getElementById('addmarker_attr_remark').value.trim();
        
        this.tool.updateMarker(this.currentEditMarkerId, {
            name: name || '我的标记',
            remark: remark
        });
        
        this.hideEditPopup();
    }
    
    /**
     * 更新标记列表
     */
    updateMarkerList() {
        const tbody = document.getElementById('addmarker_tbody');
        if (!tbody) return;
        
        const markers = this.tool.getMarkerList();
        
        // 清空列表
        tbody.innerHTML = '';
        
        // 添加标记
        markers.forEach(marker => {
            const tr = document.createElement('tr');
            
            // 名称列
            const tdName = document.createElement('td');
            tdName.textContent = marker.name || '未命名标记';
            tdName.addEventListener('click', () => {
                this.tool.centerAt(marker.id);
            });
            
            // 操作列
            const tdAction = document.createElement('td');
            tdAction.style.textAlign = 'center';
            
            const delButton = document.createElement('button');
            delButton.innerHTML = '<i class="fas fa-trash"></i>';
            delButton.style.border = 'none';
            delButton.style.background = 'none';
            delButton.style.color = '#d9534f';
            delButton.style.cursor = 'pointer';
            delButton.title = '删除';
            
            delButton.addEventListener('click', (e) => {
                e.stopPropagation();
                if (confirm(`确定要删除标记 "${marker.name}" 吗？`)) {
                    this.tool.deleteMarker(marker.id);
                }
            });
            
            tdAction.appendChild(delButton);
            
            // 添加到行
            tr.appendChild(tdName);
            tr.appendChild(tdAction);
            
            // 添加到表格
            tbody.appendChild(tr);
        });
    }
    
    /**
     * 清除文件输入框
     * @private
     */
    _clearFileInput() {
        const fileInput = document.getElementById('addmarker_file_input');
        if (fileInput) {
            fileInput.value = '';
        }
    }
    
    /**
     * 显示面板
     */
    showPanel() {
        if (!this.isPanelVisible && this.panel) {
            this.panel.style.display = 'block';
            this.isPanelVisible = true;
            
            if (this.toolButton) {
                this.toolButton.classList.add('active');
            }
            
            this.tool.activate();
            this.isActive = true;
            
            // 更新标记列表
            this.updateMarkerList();
        }
    }
    
    /**
     * 隐藏面板
     */
    hidePanel() {
        if (this.isPanelVisible && this.panel) {
            this.panel.style.display = 'none';
            this.isPanelVisible = false;
            
            if (this.toolButton) {
                this.toolButton.classList.remove('active');
            }
            
            this.tool.deactivate();
            this.isActive = false;
            
            // 隐藏编辑弹窗
            this.hideEditPopup();
        }
    }
    
    /**
     * 切换面板显示状态
     */
    togglePanel() {
        if (this.isPanelVisible) {
            this.hidePanel();
        } else {
            this.showPanel();
        }
    }
}

// 如果是ES模块环境，则导出模块
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
    module.exports = AddMarkerUI;
} else {
    window.AddMarkerUI = AddMarkerUI;
} 