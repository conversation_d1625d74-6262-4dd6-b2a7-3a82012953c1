{"version": 3, "sources": ["skmeans.js"], "names": ["e", "t", "n", "r", "s", "o", "u", "a", "require", "i", "f", "Error", "code", "l", "exports", "call", "length", "module", "root", "previous_skmeans", "skmeans", "window", "eudist", "v1", "v2", "sqrt", "len", "sum", "d", "Math", "mandist", "abs", "dist", "Distance", "kmrand", "data", "k", "map", "ks", "multi", "floor", "random", "key", "join", "push", "kmpp", "distance", "c", "dists", "lk", "dsum", "prs", "min", "Infinity", "j", "v", "pr", "cs", "sort", "b", "rnd", "idx", "ClusterInit", "MAX", "init", "val", "initial", "maxit", "old", "idxs", "conv", "it", "vlen", "count", "dif", "vsum", "vect", "h", "ksj", "sumj", "oldj", "cj", "centroids"], "mappings": ";;AAAA,CAAC,SAASA,CAAT,CAAWC,CAAX,EAAaC,CAAb,EAAeC,CAAf,EAAiB;AAAC,UAASC,CAAT,CAAWC,CAAX,EAAaC,CAAb,EAAe;AAAC,MAAG,CAACJ,EAAEG,CAAF,CAAJ,EAAS;AAAC,OAAG,CAACJ,EAAEI,CAAF,CAAJ,EAAS;AAAC,QAAIE,IAAE,OAAOC,OAAP,IAAgB,UAAhB,IAA4BA,OAAlC,CAA0C,IAAG,CAACF,CAAD,IAAIC,CAAP,EAAS,OAAOA,EAAEF,CAAF,EAAI,CAAC,CAAL,CAAP,CAAe,IAAGI,CAAH,EAAK,OAAOA,EAAEJ,CAAF,EAAI,CAAC,CAAL,CAAP,CAAe,IAAIK,IAAE,IAAIC,KAAJ,CAAU,yBAAuBN,CAAvB,GAAyB,GAAnC,CAAN,CAA8C,MAAMK,EAAEE,IAAF,GAAO,kBAAP,EAA0BF,CAAhC;AAAkC,QAAIG,IAAEX,EAAEG,CAAF,IAAK,EAACS,SAAQ,EAAT,EAAX,CAAwBb,EAAEI,CAAF,EAAK,CAAL,EAAQU,IAAR,CAAaF,EAAEC,OAAf,EAAuB,UAASd,CAAT,EAAW;AAAC,QAAIE,IAAED,EAAEI,CAAF,EAAK,CAAL,EAAQL,CAAR,CAAN,CAAiB,OAAOI,EAAEF,IAAEA,CAAF,GAAIF,CAAN,CAAP;AAAgB,IAApE,EAAqEa,CAArE,EAAuEA,EAAEC,OAAzE,EAAiFd,CAAjF,EAAmFC,CAAnF,EAAqFC,CAArF,EAAuFC,CAAvF;AAA0F,UAAOD,EAAEG,CAAF,EAAKS,OAAZ;AAAoB,MAAIL,IAAE,OAAOD,OAAP,IAAgB,UAAhB,IAA4BA,OAAlC,CAA0C,KAAI,IAAIH,IAAE,CAAV,EAAYA,IAAEF,EAAEa,MAAhB,EAAuBX,GAAvB;AAA2BD,IAAED,EAAEE,CAAF,CAAF;AAA3B,EAAmC,OAAOD,CAAP;AAAS,CAAzb,EAA2b,EAAC,GAAE,CAAC,UAASI,OAAT,EAAiBS,MAAjB,EAAwBH,OAAxB,EAAgC;AAC/d;;AAEA,GAAC,YAAW;AACV,OAAII,OAAO,IAAX;AACA,OAAIC,mBAAmBD,KAAKE,OAA5B;AACD,OAAIA,UAAUZ,QAAQ,WAAR,CAAd;;AAEA,OAAI,OAAOM,OAAP,KAAmB,WAAvB,EAAqC;AAClC,QAAI,OAAOG,MAAP,KAAkB,WAAlB,IAAiCA,OAAOH,OAA5C,EAAsD;AACpDA,eAAUG,OAAOH,OAAP,GAAiBM,OAA3B;AACD;AACDN,YAAQM,OAAR,GAAkBA,OAAlB;AACD;;AAEF,OAAG,OAAOC,MAAP,KAAkB,WAArB,EAAkC;AAC/BA,WAAOD,OAAP,GAAiBA,OAAjB;AACD;AAEF,GAhBD,EAgBGL,IAhBH,CAgBQ,IAhBR;AAkBC,EArB6b,EAqB5b,EAAC,aAAY,CAAb,EArB4b,CAAH,EAqBxa,GAAE,CAAC,UAASP,OAAT,EAAiBS,MAAjB,EAAwBH,OAAxB,EAAgC;AACtDG,SAAOH,OAAP,GAAiB;AAChB;;;AAGAQ,SAJgB,kBAITC,EAJS,EAINC,EAJM,EAIHC,IAJG,EAIG;AAClB,QAAIC,MAAMH,GAAGP,MAAb;AACA,QAAIW,MAAM,CAAV;;AAEA,SAAI,IAAIlB,IAAE,CAAV,EAAYA,IAAEiB,GAAd,EAAkBjB,GAAlB,EAAuB;AACtB,SAAImB,IAAI,CAACL,GAAGd,CAAH,KAAO,CAAR,KAAce,GAAGf,CAAH,KAAO,CAArB,CAAR;AACAkB,YAAOC,IAAEA,CAAT;AACA;AACD;AACA,WAAOH,OAAMI,KAAKJ,IAAL,CAAUE,GAAV,CAAN,GAAuBA,GAA9B;AACA,IAde;AAgBhBG,UAhBgB,mBAgBRP,EAhBQ,EAgBLC,EAhBK,EAgBFC,IAhBE,EAgBI;AACnB,QAAIC,MAAMH,GAAGP,MAAb;AACA,QAAIW,MAAM,CAAV;;AAEA,SAAI,IAAIlB,IAAE,CAAV,EAAYA,IAAEiB,GAAd,EAAkBjB,GAAlB,EAAuB;AACtBkB,YAAOE,KAAKE,GAAL,CAAS,CAACR,GAAGd,CAAH,KAAO,CAAR,KAAce,GAAGf,CAAH,KAAO,CAArB,CAAT,CAAP;AACA;;AAED;AACA,WAAOgB,OAAMI,KAAKJ,IAAL,CAAUE,GAAV,CAAN,GAAuBA,GAA9B;AACA,IA1Be;;;AA4BhB;;;AAGAK,OA/BgB,gBA+BXT,EA/BW,EA+BRC,EA/BQ,EA+BLC,IA/BK,EA+BC;AAChB,QAAIG,IAAIC,KAAKE,GAAL,CAASR,KAAGC,EAAZ,CAAR;AACA,WAAOC,OAAMG,CAAN,GAAUA,IAAEA,CAAnB;AACA;AAlCe,GAAjB;AAsCC,EAvCoB,EAuCnB,EAvCmB,CArBsa,EA4Drb,GAAE,CAAC,UAASpB,OAAT,EAAiBS,MAAjB,EAAwBH,OAAxB,EAAgC;AACzC,MACCmB,WAAWzB,QAAQ,eAAR,CADZ;AAAA,MAECc,SAASW,SAASX,MAFnB;AAAA,MAGCU,OAAOC,SAASD,IAHjB;;AAKAf,SAAOH,OAAP,GAAiB;AAChBoB,SADgB,kBACTC,IADS,EACJC,CADI,EACD;AACd,QAAIC,MAAM,EAAV;AAAA,QAAcC,KAAK,EAAnB;AAAA,QAAuBrC,IAAImC,KAAG,CAA9B;AACA,QAAIV,MAAMS,KAAKnB,MAAf;AACA,QAAIuB,QAAQJ,KAAK,CAAL,EAAQnB,MAAR,GAAe,CAA3B;;AAEA,WAAMsB,GAAGtB,MAAH,GAAUoB,CAAV,IAAgBnC,GAAD,GAAM,CAA3B,EAA8B;AAC7B,SAAI2B,IAAIO,KAAKN,KAAKW,KAAL,CAAWX,KAAKY,MAAL,KAAcf,GAAzB,CAAL,CAAR;AACA,SAAIgB,MAAMH,QAAOX,EAAEe,IAAF,CAAO,GAAP,CAAP,QAAwBf,CAAlC;AACA,SAAG,CAACS,IAAIK,GAAJ,CAAJ,EAAc;AACbL,UAAIK,GAAJ,IAAW,IAAX;AACAJ,SAAGM,IAAH,CAAQhB,CAAR;AACA;AACD;;AAED,QAAGU,GAAGtB,MAAH,GAAUoB,CAAb,EAAgB,MAAM,IAAIzB,KAAJ,CAAU,+BAAV,CAAN,CAAhB,KACK,OAAO2B,EAAP;AACL,IAjBe;;;AAmBhB;;;AAGAO,OAtBgB,gBAsBXV,IAtBW,EAsBNC,CAtBM,EAsBH;AACZ,QAAIU,WAAWX,KAAK,CAAL,EAAQnB,MAAR,GAAgBM,MAAhB,GAAyBU,IAAxC;AACA,QAAIM,KAAK,EAAT;AAAA,QAAaZ,MAAMS,KAAKnB,MAAxB;AACA,QAAIuB,QAAQJ,KAAK,CAAL,EAAQnB,MAAR,GAAe,CAA3B;AACA,QAAIqB,MAAM,EAAV;;AAEA;AACA,QAAIU,IAAIZ,KAAKN,KAAKW,KAAL,CAAWX,KAAKY,MAAL,KAAcf,GAAzB,CAAL,CAAR;AACA,QAAIgB,MAAMH,QAAOQ,EAAEJ,IAAF,CAAO,GAAP,CAAP,QAAwBI,CAAlC;AACAT,OAAGM,IAAH,CAAQG,CAAR;AACAV,QAAIK,GAAJ,IAAW,IAAX;;AAEA;AACA,WAAMJ,GAAGtB,MAAH,GAAUoB,CAAhB,EAAmB;AAClB;AACA,SAAIY,QAAQ,EAAZ;AAAA,SAAgBC,KAAKX,GAAGtB,MAAxB;AACA,SAAIkC,OAAO,CAAX;AAAA,SAAcC,MAAM,EAApB;;AAEA,UAAI,IAAI1C,IAAE,CAAV,EAAYA,IAAEiB,GAAd,EAAkBjB,GAAlB,EAAuB;AACtB,UAAI2C,MAAMC,QAAV;AACA,WAAI,IAAIC,IAAE,CAAV,EAAYA,IAAEL,EAAd,EAAiBK,GAAjB,EAAsB;AACrB,WAAItB,QAAOc,SAASX,KAAK1B,CAAL,CAAT,EAAiB6B,GAAGgB,CAAH,CAAjB,CAAX;AACA,WAAGtB,SAAMoB,GAAT,EAAcA,MAAMpB,KAAN;AACd;AACDgB,YAAMvC,CAAN,IAAW2C,GAAX;AACA;;AAED;AACA,UAAI,IAAI3C,KAAE,CAAV,EAAYA,KAAEiB,GAAd,EAAkBjB,IAAlB,EAAuB;AACtByC,cAAQF,MAAMvC,EAAN,CAAR;AACA;;AAED;AACA,UAAI,IAAIA,MAAE,CAAV,EAAYA,MAAEiB,GAAd,EAAkBjB,KAAlB,EAAuB;AACtB0C,UAAI1C,GAAJ,IAAS,EAACA,GAAEA,GAAH,EAAM8C,GAAEpB,KAAK1B,GAAL,CAAR,EAAiB+C,IAAGR,MAAMvC,GAAN,IAASyC,IAA7B,EAAmCO,IAAG,CAAtC,EAAT;AACA;;AAED;AACAN,SAAIO,IAAJ,CAAS,UAACnD,CAAD,EAAGoD,CAAH;AAAA,aAAOpD,EAAEiD,EAAF,GAAKG,EAAEH,EAAd;AAAA,MAAT;;AAEA;AACAL,SAAI,CAAJ,EAAOM,EAAP,GAAYN,IAAI,CAAJ,EAAOK,EAAnB;AACA,UAAI,IAAI/C,MAAE,CAAV,EAAYA,MAAEiB,GAAd,EAAkBjB,KAAlB,EAAuB;AACtB0C,UAAI1C,GAAJ,EAAOgD,EAAP,GAAYN,IAAI1C,MAAE,CAAN,EAASgD,EAAT,GAAcN,IAAI1C,GAAJ,EAAO+C,EAAjC;AACA;;AAED;AACA,SAAII,MAAM/B,KAAKY,MAAL,EAAV;;AAEA;AACA,SAAIoB,MAAM,CAAV;AACA,YAAMA,MAAInC,MAAI,CAAR,IAAayB,IAAIU,KAAJ,EAAWJ,EAAX,GAAcG,GAAjC;AACAtB,QAAGM,IAAH,CAAQO,IAAIU,MAAI,CAAR,EAAWN,CAAnB;AACA;;;;;;;;;;;;;;;;AAgBA;;AAED,WAAOjB,EAAP;AACA;AA9Fe,GAAjB;AAkGC,EAxGO,EAwGN,EAAC,iBAAgB,CAAjB,EAxGM,CA5Dmb,EAoKpa,GAAE,CAAC,UAAS9B,OAAT,EAAiBS,MAAjB,EAAwBH,OAAxB,EAAgC;AAC1D;;AAEA,MACCmB,WAAWzB,QAAQ,eAAR,CADZ;AAAA,MAECsD,cAActD,QAAQ,YAAR,CAFf;AAAA,MAGCc,SAASW,SAASX,MAHnB;AAAA,MAICQ,UAAUG,SAASH,OAJpB;AAAA,MAKCE,OAAOC,SAASD,IALjB;AAAA,MAMCE,SAAS4B,YAAY5B,MANtB;AAAA,MAOCW,OAAOiB,YAAYjB,IAPpB;;AASA,MAAMkB,MAAM,KAAZ;;AAEA;;;AAGA,WAASC,IAAT,CAActC,GAAd,EAAkBuC,GAAlB,EAAsBV,CAAtB,EAAyB;AACxBA,OAAIA,KAAK,EAAT;AACA,QAAI,IAAI9C,IAAE,CAAV,EAAYA,IAAEiB,GAAd,EAAkBjB,GAAlB;AAAuB8C,MAAE9C,CAAF,IAAOwD,GAAP;AAAvB,IACA,OAAOV,CAAP;AACA;;AAED,WAASnC,OAAT,CAAiBe,IAAjB,EAAsBC,CAAtB,EAAwB8B,OAAxB,EAAgCC,KAAhC,EAAuC;AACtC,OAAI7B,KAAK,EAAT;AAAA,OAAa8B,MAAM,EAAnB;AAAA,OAAuBC,OAAO,EAA9B;AAAA,OAAkCrC,OAAO,EAAzC;AACA,OAAIsC,OAAO,KAAX;AAAA,OAAkBC,KAAKJ,SAASJ,GAAhC;AACA,OAAIrC,MAAMS,KAAKnB,MAAf;AAAA,OAAuBwD,OAAOrC,KAAK,CAAL,EAAQnB,MAAtC;AAAA,OAA8CuB,QAAQiC,OAAK,CAA3D;AACA,OAAIC,QAAQ,EAAZ;;AAEA,OAAG,CAACP,OAAJ,EAAa;AACZ,QAAIG,QAAO,EAAX;AACA,WAAM/B,GAAGtB,MAAH,GAAUoB,CAAhB,EAAmB;AAClB,SAAIyB,MAAMhC,KAAKW,KAAL,CAAWX,KAAKY,MAAL,KAAcf,GAAzB,CAAV;AACA,SAAG,CAAC2C,MAAKR,GAAL,CAAJ,EAAe;AACdQ,YAAKR,GAAL,IAAY,IAAZ;AACAvB,SAAGM,IAAH,CAAQT,KAAK0B,GAAL,CAAR;AACA;AACD;AACD,IATD,MAUK,IAAGK,WAAS,QAAZ,EAAsB;AAC1B5B,SAAKJ,OAAOC,IAAP,EAAYC,CAAZ,CAAL;AACA,IAFI,MAGA,IAAG8B,WAAS,MAAZ,EAAoB;AACxB5B,SAAKO,KAAKV,IAAL,EAAUC,CAAV,CAAL;AACA,IAFI,MAGA;AACJE,SAAK4B,OAAL;AACA;;AAED,MAAG;AACF;AACAF,SAAK5B,CAAL,EAAO,CAAP,EAASqC,KAAT;;AAEA;AACA,SAAI,IAAIhE,IAAE,CAAV,EAAYA,IAAEiB,GAAd,EAAkBjB,GAAlB,EAAuB;AACtB,SAAI2C,MAAMC,QAAV;AAAA,SAAoBQ,OAAM,CAA1B;AACA,UAAI,IAAIP,IAAE,CAAV,EAAYA,IAAElB,CAAd,EAAgBkB,GAAhB,EAAqB;AACpB;AACA,UAAItB,OAAOO,QAAOjB,OAAOa,KAAK1B,CAAL,CAAP,EAAe6B,GAAGgB,CAAH,CAAf,CAAP,GAA+BzB,KAAKE,GAAL,CAASI,KAAK1B,CAAL,IAAQ6B,GAAGgB,CAAH,CAAjB,CAA1C;AACA,UAAGtB,QAAMoB,GAAT,EAAc;AACbA,aAAMpB,IAAN;AACA6B,cAAMP,CAAN;AACA;AACD;AACDe,UAAK5D,CAAL,IAAUoD,IAAV,CAVsB,CAUP;AACfY,WAAMZ,IAAN,IAXsB,CAWP;AACf;;AAED;AACA,QAAIlC,MAAM,EAAV;AAAA,QAAcyC,MAAM,EAApB;AAAA,QAAwBM,MAAM,CAA9B;AACA,SAAI,IAAIpB,KAAE,CAAV,EAAYA,KAAElB,CAAd,EAAgBkB,IAAhB,EAAqB;AACpB;AACA3B,SAAI2B,EAAJ,IAASf,QAAOyB,KAAKQ,IAAL,EAAU,CAAV,EAAY7C,IAAI2B,EAAJ,CAAZ,CAAP,GAA6B,CAAtC;AACAc,SAAId,EAAJ,IAAShB,GAAGgB,EAAH,CAAT;AACA;;AAED;AACA,QAAGf,KAAH,EAAU;AACT,UAAI,IAAIe,MAAE,CAAV,EAAYA,MAAElB,CAAd,EAAgBkB,KAAhB;AAAqBhB,SAAGgB,GAAH,IAAQ,EAAR;AAArB,MADS,CAGT;AACA,UAAI,IAAI7C,MAAE,CAAV,EAAYA,MAAEiB,GAAd,EAAkBjB,KAAlB,EAAuB;AACtB,UAAIoD,QAAMQ,KAAK5D,GAAL,CAAV;AAAA,UAAoB;AAClBkE,aAAOhD,IAAIkC,KAAJ,CADT;AAAA,UACmB;AACjBe,aAAOzC,KAAK1B,GAAL,CAFT,CADsB,CAGH;;AAEnB;AACA,WAAI,IAAIoE,IAAE,CAAV,EAAYA,IAAEL,IAAd,EAAmBK,GAAnB,EAAwB;AACvBF,YAAKE,CAAL,KAAWD,KAAKC,CAAL,CAAX;AACA;AACD;AACD;AACAP,YAAO,IAAP;AACA,UAAI,IAAIhB,MAAE,CAAV,EAAYA,MAAElB,CAAd,EAAgBkB,KAAhB,EAAqB;AACpB,UAAIwB,MAAMxC,GAAGgB,GAAH,CAAV;AAAA,UAAkB;AAChByB,aAAOpD,IAAI2B,GAAJ,CADT;AAAA,UACiB;AACf0B,aAAOZ,IAAId,GAAJ,CAFT;AAAA,UAEkB;AAChB2B,WAAKR,MAAMnB,GAAN,CAHP,CADoB,CAIH;;AAEjB;AACA,WAAI,IAAIuB,KAAE,CAAV,EAAYA,KAAEL,IAAd,EAAmBK,IAAnB,EAAwB;AACvBC,WAAID,EAAJ,IAAUE,KAAKF,EAAL,CAAD,GAAWI,EAAX,IAAkB,CAA3B,CADuB,CACO;AAC9B;;AAED;AACA,UAAGX,IAAH,EAAS;AACR,YAAI,IAAIO,MAAE,CAAV,EAAYA,MAAEL,IAAd,EAAmBK,KAAnB,EAAwB;AACvB,YAAGG,KAAKH,GAAL,KAASC,IAAID,GAAJ,CAAZ,EAAoB;AACnBP,gBAAO,KAAP;AACA;AACA;AACD;AACD;AACD;AACD;AACD;AAtCA,SAuCK;AACJ;AACA,WAAI,IAAI7D,MAAE,CAAV,EAAYA,MAAEiB,GAAd,EAAkBjB,KAAlB,EAAuB;AACtB,WAAIoD,QAAMQ,KAAK5D,GAAL,CAAV;AACAkB,WAAIkC,KAAJ,KAAY1B,KAAK1B,GAAL,CAAZ;AACA;AACD;AACA,WAAI,IAAI6C,MAAE,CAAV,EAAYA,MAAElB,CAAd,EAAgBkB,KAAhB,EAAqB;AACpBhB,UAAGgB,GAAH,IAAQ3B,IAAI2B,GAAJ,IAAOmB,MAAMnB,GAAN,CAAP,IAAmB,CAA3B,CADoB,CACU;AAC9B;AACD;AACAgB,aAAO,IAAP;AACA,WAAI,IAAIhB,MAAE,CAAV,EAAYA,MAAElB,CAAd,EAAgBkB,KAAhB,EAAqB;AACpB,WAAGc,IAAId,GAAJ,KAAQhB,GAAGgB,GAAH,CAAX,EAAkB;AACjBgB,eAAO,KAAP;AACA;AACA;AACD;AACD;;AAEDA,WAAOA,QAAS,EAAEC,EAAF,IAAM,CAAtB;AACA,IAxFD,QAwFO,CAACD,IAxFR;;AA0FA,UAAO;AACNC,QAAKR,MAAIQ,EADH;AAENnC,OAAIA,CAFE;AAGNiC,UAAOA,IAHD;AAINa,eAAY5C;AAJN,IAAP;AAMA;;AAEDrB,SAAOH,OAAP,GAAiBM,OAAjB;AAEC,EArJwB,EAqJvB,EAAC,iBAAgB,CAAjB,EAAmB,cAAa,CAAhC,EArJuB,CApKka,EAA3b,EAyTuC,EAzTvC,EAyT0C,CAAC,CAAD,CAzT1C", "file": "skmeans.js", "sourcesContent": ["(function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require==\"function\"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error(\"Cannot find module '\"+o+\"'\");throw f.code=\"MODULE_NOT_FOUND\",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require==\"function\"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){\n\"use strict\";\n\n(function() {\n  var root = this\n  var previous_skmeans = root.skmeans;\n\tvar skmeans = require('./main.js');\n\n\tif( typeof exports !== 'undefined' ) {\n    if( typeof module !== 'undefined' && module.exports ) {\n      exports = module.exports = skmeans;\n    }\n    exports.skmeans = skmeans;\n  }\n\n\tif(typeof window !== 'undefined') {\n    window.skmeans = skmeans;\n  }\n\n}).call(this);\n\n},{\"./main.js\":4}],2:[function(require,module,exports){\nmodule.exports = {\n\t/**\n\t * Euclidean distance\n\t */\n\teudist(v1,v2,sqrt) {\n\t\tvar len = v1.length;\n\t\tvar sum = 0;\n\n\t\tfor(let i=0;i<len;i++) {\n\t\t\tvar d = (v1[i]||0) - (v2[i]||0);\n\t\t\tsum += d*d;\n\t\t}\n\t\t// Square root not really needed\n\t\treturn sqrt? Math.sqrt(sum) : sum;\n\t},\n\n\tmandist(v1,v2,sqrt) {\n\t\tvar len = v1.length;\n\t\tvar sum = 0;\n\n\t\tfor(let i=0;i<len;i++) {\n\t\t\tsum += Math.abs((v1[i]||0) - (v2[i]||0));\n\t\t}\n\n\t\t// Square root not really needed\n\t\treturn sqrt? Math.sqrt(sum) : sum;\n\t},\n\n\t/**\n\t * Unidimensional distance\n\t */\n\tdist(v1,v2,sqrt) {\n\t\tvar d = Math.abs(v1-v2);\n\t\treturn sqrt? d : d*d;\n\t}\n\n}\n\n},{}],3:[function(require,module,exports){\nconst\n\tDistance = require(\"./distance.js\"),\n\teudist = Distance.eudist,\n\tdist = Distance.dist;\n\nmodule.exports = {\n\tkmrand(data,k) {\n\t\tvar map = {}, ks = [], t = k<<2;\n\t\tvar len = data.length;\n\t\tvar multi = data[0].length>0;\n\n\t\twhile(ks.length<k && (t--)>0) {\n\t\t\tlet d = data[Math.floor(Math.random()*len)];\n\t\t\tlet key = multi? d.join(\"_\") : `${d}`;\n\t\t\tif(!map[key]) {\n\t\t\t\tmap[key] = true;\n\t\t\t\tks.push(d);\n\t\t\t}\n\t\t}\n\n\t\tif(ks.length<k) throw new Error(\"Error initializating clusters\");\n\t\telse return ks;\n\t},\n\n\t/**\n\t * K-means++ initial centroid selection\n\t */\n\tkmpp(data,k) {\n\t\tvar distance = data[0].length? eudist : dist;\n\t\tvar ks = [], len = data.length;\n\t\tvar multi = data[0].length>0;\n\t\tvar map = {};\n\n\t\t// First random centroid\n\t\tvar c = data[Math.floor(Math.random()*len)];\n\t\tvar key = multi? c.join(\"_\") : `${c}`;\n\t\tks.push(c);\n\t\tmap[key] = true;\n\n\t\t// Retrieve next centroids\n\t\twhile(ks.length<k) {\n\t\t\t// Min Distances between current centroids and data points\n\t\t\tlet dists = [], lk = ks.length;\n\t\t\tlet dsum = 0, prs = [];\n\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tlet min = Infinity;\n\t\t\t\tfor(let j=0;j<lk;j++) {\n\t\t\t\t\tlet dist = distance(data[i],ks[j]);\n\t\t\t\t\tif(dist<=min) min = dist;\n\t\t\t\t}\n\t\t\t\tdists[i] = min;\n\t\t\t}\n\n\t\t\t// Sum all min distances\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tdsum += dists[i]\n\t\t\t}\n\n\t\t\t// Probabilities and cummulative prob (cumsum)\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tprs[i] = {i:i, v:data[i],\tpr:dists[i]/dsum, cs:0}\n\t\t\t}\n\n\t\t\t// Sort Probabilities\n\t\t\tprs.sort((a,b)=>a.pr-b.pr);\n\n\t\t\t// Cummulative Probabilities\n\t\t\tprs[0].cs = prs[0].pr;\n\t\t\tfor(let i=1;i<len;i++) {\n\t\t\t\tprs[i].cs = prs[i-1].cs + prs[i].pr;\n\t\t\t}\n\n\t\t\t// Randomize\n\t\t\tlet rnd = Math.random();\n\n\t\t\t// Gets only the items whose cumsum >= rnd\n\t\t\tlet idx = 0;\n\t\t\twhile(idx<len-1 && prs[idx++].cs<rnd);\n\t\t\tks.push(prs[idx-1].v);\n\t\t\t/*\n\t\t\tlet done = false;\n\t\t\twhile(!done) {\n\t\t\t\t// this is our new centroid\n\t\t\t\tc = prs[idx-1].v\n\t\t\t\tkey = multi? c.join(\"_\") : `${c}`;\n\t\t\t\tif(!map[key]) {\n\t\t\t\t\tmap[key] = true;\n\t\t\t\t\tks.push(c);\n\t\t\t\t\tdone = true;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tidx++;\n\t\t\t\t}\n\t\t\t}\n\t\t\t*/\n\t\t}\n\n\t\treturn ks;\n\t}\n\n}\n\n},{\"./distance.js\":2}],4:[function(require,module,exports){\n/*jshint esversion: 6 */\n\nconst\n\tDistance = require(\"./distance.js\"),\n\tClusterInit = require(\"./kinit.js\"),\n\teudist = Distance.eudist,\n\tmandist = Distance.mandist,\n\tdist = Distance.dist,\n\tkmrand = ClusterInit.kmrand,\n\tkmpp = ClusterInit.kmpp;\n\nconst MAX = 10000;\n\n/**\n * Inits an array with values\n */\nfunction init(len,val,v) {\n\tv = v || [];\n\tfor(let i=0;i<len;i++) v[i] = val;\n\treturn v;\n}\n\nfunction skmeans(data,k,initial,maxit) {\n\tvar ks = [], old = [], idxs = [], dist = [];\n\tvar conv = false, it = maxit || MAX;\n\tvar len = data.length, vlen = data[0].length, multi = vlen>0;\n\tvar count = [];\n\n\tif(!initial) {\n\t\tlet idxs = {};\n\t\twhile(ks.length<k) {\n\t\t\tlet idx = Math.floor(Math.random()*len);\n\t\t\tif(!idxs[idx]) {\n\t\t\t\tidxs[idx] = true;\n\t\t\t\tks.push(data[idx]);\n\t\t\t}\n\t\t}\n\t}\n\telse if(initial==\"kmrand\") {\n\t\tks = kmrand(data,k);\n\t}\n\telse if(initial==\"kmpp\") {\n\t\tks = kmpp(data,k);\n\t}\n\telse {\n\t\tks = initial;\n\t}\n\n\tdo {\n\t\t// Reset k count\n\t\tinit(k,0,count);\n\n\t\t// For each value in data, find the nearest centroid\n\t\tfor(let i=0;i<len;i++) {\n\t\t\tlet min = Infinity, idx = 0;\n\t\t\tfor(let j=0;j<k;j++) {\n\t\t\t\t// Multidimensional or unidimensional\n\t\t\t\tvar dist = multi? eudist(data[i],ks[j]) : Math.abs(data[i]-ks[j]);\n\t\t\t\tif(dist<=min) {\n\t\t\t\t\tmin = dist;\n\t\t\t\t\tidx = j;\n\t\t\t\t}\n\t\t\t}\n\t\t\tidxs[i] = idx;\t// Index of the selected centroid for that value\n\t\t\tcount[idx]++;\t\t// Number of values for this centroid\n\t\t}\n\n\t\t// Recalculate centroids\n\t\tvar sum = [], old = [], dif = 0;\n\t\tfor(let j=0;j<k;j++) {\n\t\t\t// Multidimensional or unidimensional\n\t\t\tsum[j] = multi? init(vlen,0,sum[j]) : 0;\n\t\t\told[j] = ks[j];\n\t\t}\n\n\t\t// If multidimensional\n\t\tif(multi) {\n\t\t\tfor(let j=0;j<k;j++) ks[j] = [];\n\n\t\t\t// Sum values and count for each centroid\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tlet\tidx = idxs[i],\t\t// Centroid for that item\n\t\t\t\t\t\tvsum = sum[idx],\t// Sum values for this centroid\n\t\t\t\t\t\tvect = data[i];\t\t// Current vector\n\n\t\t\t\t// Accumulate value on the centroid for current vector\n\t\t\t\tfor(let h=0;h<vlen;h++) {\n\t\t\t\t\tvsum[h] += vect[h];\n\t\t\t\t}\n\t\t\t}\n\t\t\t// Calculate the average for each centroid\n\t\t\tconv = true;\n\t\t\tfor(let j=0;j<k;j++) {\n\t\t\t\tlet ksj = ks[j],\t\t// Current centroid\n\t\t\t\t\t\tsumj = sum[j],\t// Accumulated centroid values\n\t\t\t\t\t\toldj = old[j], \t// Old centroid value\n\t\t\t\t\t\tcj = count[j];\t// Number of elements for this centroid\n\n\t\t\t\t// New average\n\t\t\t\tfor(let h=0;h<vlen;h++) {\n\t\t\t\t\tksj[h] = (sumj[h])/(cj) || 0;\t// New centroid\n\t\t\t\t}\n\n\t\t\t\t// Find if centroids have moved\n\t\t\t\tif(conv) {\n\t\t\t\t\tfor(let h=0;h<vlen;h++) {\n\t\t\t\t\t\tif(oldj[h]!=ksj[h]) {\n\t\t\t\t\t\t\tconv = false;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t// If unidimensional\n\t\telse {\n\t\t\t// Sum values and count for each centroid\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tlet idx = idxs[i];\n\t\t\t\tsum[idx] += data[i];\n\t\t\t}\n\t\t\t// Calculate the average for each centroid\n\t\t\tfor(let j=0;j<k;j++) {\n\t\t\t\tks[j] = sum[j]/count[j] || 0;\t// New centroid\n\t\t\t}\n\t\t\t// Find if centroids have moved\n\t\t\tconv = true;\n\t\t\tfor(let j=0;j<k;j++) {\n\t\t\t\tif(old[j]!=ks[j]) {\n\t\t\t\t\tconv = false;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconv = conv || (--it<=0);\n\t}while(!conv);\n\n\treturn {\n\t\tit : MAX-it,\n\t\tk : k,\n\t\tidxs : idxs,\n\t\tcentroids : ks\n\t};\n}\n\nmodule.exports = skmeans;\n\n},{\"./distance.js\":2,\"./kinit.js\":3}]},{},[1]);\n"]}