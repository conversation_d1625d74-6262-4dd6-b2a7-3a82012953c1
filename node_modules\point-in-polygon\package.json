{"name": "point-in-polygon", "description": "determine if a point is inside a polygon with a ray intersection counting algorithm", "version": "1.1.0", "repository": {"type": "git", "url": "git://github.com/substack/point-in-polygon.git"}, "main": "index.js", "keywords": ["point", "polygon", "inside"], "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "^4.0.0"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}}