{"version": 3, "sources": ["../../kinit.js"], "names": ["Distance", "require", "eudist", "dist", "module", "exports", "kmrand", "data", "k", "map", "ks", "t", "len", "length", "multi", "d", "Math", "floor", "random", "key", "join", "push", "Error", "kmpp", "distance", "c", "dists", "lk", "dsum", "prs", "i", "min", "Infinity", "j", "v", "pr", "cs", "sort", "a", "b", "rnd", "idx"], "mappings": ";;AAAA,IACCA,WAAWC,QAAQ,eAAR,CADZ;AAAA,IAECC,SAASF,SAASE,MAFnB;AAAA,IAGCC,OAAOH,SAASG,IAHjB;;AAKAC,OAAOC,OAAP,GAAiB;AAChBC,OADgB,kBACTC,IADS,EACJC,CADI,EACD;AACd,MAAIC,MAAM,EAAV;AAAA,MAAcC,KAAK,EAAnB;AAAA,MAAuBC,IAAIH,KAAG,CAA9B;AACA,MAAII,MAAML,KAAKM,MAAf;AACA,MAAIC,QAAQP,KAAK,CAAL,EAAQM,MAAR,GAAe,CAA3B;;AAEA,SAAMH,GAAGG,MAAH,GAAUL,CAAV,IAAgBG,GAAD,GAAM,CAA3B,EAA8B;AAC7B,OAAII,IAAIR,KAAKS,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAcN,GAAzB,CAAL,CAAR;AACA,OAAIO,MAAML,QAAOC,EAAEK,IAAF,CAAO,GAAP,CAAP,QAAwBL,CAAlC;AACA,OAAG,CAACN,IAAIU,GAAJ,CAAJ,EAAc;AACbV,QAAIU,GAAJ,IAAW,IAAX;AACAT,OAAGW,IAAH,CAAQN,CAAR;AACA;AACD;;AAED,MAAGL,GAAGG,MAAH,GAAUL,CAAb,EAAgB,MAAM,IAAIc,KAAJ,CAAU,+BAAV,CAAN,CAAhB,KACK,OAAOZ,EAAP;AACL,EAjBe;;;AAmBhB;;;AAGAa,KAtBgB,gBAsBXhB,IAtBW,EAsBNC,CAtBM,EAsBH;AACZ,MAAIgB,WAAWjB,KAAK,CAAL,EAAQM,MAAR,GAAgBX,MAAhB,GAAyBC,IAAxC;AACA,MAAIO,KAAK,EAAT;AAAA,MAAaE,MAAML,KAAKM,MAAxB;AACA,MAAIC,QAAQP,KAAK,CAAL,EAAQM,MAAR,GAAe,CAA3B;AACA,MAAIJ,MAAM,EAAV;;AAEA;AACA,MAAIgB,IAAIlB,KAAKS,KAAKC,KAAL,CAAWD,KAAKE,MAAL,KAAcN,GAAzB,CAAL,CAAR;AACA,MAAIO,MAAML,QAAOW,EAAEL,IAAF,CAAO,GAAP,CAAP,QAAwBK,CAAlC;AACAf,KAAGW,IAAH,CAAQI,CAAR;AACAhB,MAAIU,GAAJ,IAAW,IAAX;;AAEA;AACA,SAAMT,GAAGG,MAAH,GAAUL,CAAhB,EAAmB;AAClB;AACA,OAAIkB,QAAQ,EAAZ;AAAA,OAAgBC,KAAKjB,GAAGG,MAAxB;AACA,OAAIe,OAAO,CAAX;AAAA,OAAcC,MAAM,EAApB;;AAEA,QAAI,IAAIC,IAAE,CAAV,EAAYA,IAAElB,GAAd,EAAkBkB,GAAlB,EAAuB;AACtB,QAAIC,MAAMC,QAAV;AACA,SAAI,IAAIC,IAAE,CAAV,EAAYA,IAAEN,EAAd,EAAiBM,GAAjB,EAAsB;AACrB,SAAI9B,QAAOqB,SAASjB,KAAKuB,CAAL,CAAT,EAAiBpB,GAAGuB,CAAH,CAAjB,CAAX;AACA,SAAG9B,SAAM4B,GAAT,EAAcA,MAAM5B,KAAN;AACd;AACDuB,UAAMI,CAAN,IAAWC,GAAX;AACA;;AAED;AACA,QAAI,IAAID,KAAE,CAAV,EAAYA,KAAElB,GAAd,EAAkBkB,IAAlB,EAAuB;AACtBF,YAAQF,MAAMI,EAAN,CAAR;AACA;;AAED;AACA,QAAI,IAAIA,MAAE,CAAV,EAAYA,MAAElB,GAAd,EAAkBkB,KAAlB,EAAuB;AACtBD,QAAIC,GAAJ,IAAS,EAACA,GAAEA,GAAH,EAAMI,GAAE3B,KAAKuB,GAAL,CAAR,EAAiBK,IAAGT,MAAMI,GAAN,IAASF,IAA7B,EAAmCQ,IAAG,CAAtC,EAAT;AACA;;AAED;AACAP,OAAIQ,IAAJ,CAAS,UAACC,CAAD,EAAGC,CAAH;AAAA,WAAOD,EAAEH,EAAF,GAAKI,EAAEJ,EAAd;AAAA,IAAT;;AAEA;AACAN,OAAI,CAAJ,EAAOO,EAAP,GAAYP,IAAI,CAAJ,EAAOM,EAAnB;AACA,QAAI,IAAIL,MAAE,CAAV,EAAYA,MAAElB,GAAd,EAAkBkB,KAAlB,EAAuB;AACtBD,QAAIC,GAAJ,EAAOM,EAAP,GAAYP,IAAIC,MAAE,CAAN,EAASM,EAAT,GAAcP,IAAIC,GAAJ,EAAOK,EAAjC;AACA;;AAED;AACA,OAAIK,MAAMxB,KAAKE,MAAL,EAAV;;AAEA;AACA,OAAIuB,MAAM,CAAV;AACA,UAAMA,MAAI7B,MAAI,CAAR,IAAaiB,IAAIY,KAAJ,EAAWL,EAAX,GAAcI,GAAjC;AACA9B,MAAGW,IAAH,CAAQQ,IAAIY,MAAI,CAAR,EAAWP,CAAnB;AACA;;;;;;;;;;;;;;;;AAgBA;;AAED,SAAOxB,EAAP;AACA;AA9Fe,CAAjB", "file": "kinit.js", "sourcesContent": ["const\n\tDistance = require(\"./distance.js\"),\n\teudist = Distance.eudist,\n\tdist = Distance.dist;\n\nmodule.exports = {\n\tkmrand(data,k) {\n\t\tvar map = {}, ks = [], t = k<<2;\n\t\tvar len = data.length;\n\t\tvar multi = data[0].length>0;\n\n\t\twhile(ks.length<k && (t--)>0) {\n\t\t\tlet d = data[Math.floor(Math.random()*len)];\n\t\t\tlet key = multi? d.join(\"_\") : `${d}`;\n\t\t\tif(!map[key]) {\n\t\t\t\tmap[key] = true;\n\t\t\t\tks.push(d);\n\t\t\t}\n\t\t}\n\n\t\tif(ks.length<k) throw new Error(\"Error initializating clusters\");\n\t\telse return ks;\n\t},\n\n\t/**\n\t * K-means++ initial centroid selection\n\t */\n\tkmpp(data,k) {\n\t\tvar distance = data[0].length? eudist : dist;\n\t\tvar ks = [], len = data.length;\n\t\tvar multi = data[0].length>0;\n\t\tvar map = {};\n\n\t\t// First random centroid\n\t\tvar c = data[Math.floor(Math.random()*len)];\n\t\tvar key = multi? c.join(\"_\") : `${c}`;\n\t\tks.push(c);\n\t\tmap[key] = true;\n\n\t\t// Retrieve next centroids\n\t\twhile(ks.length<k) {\n\t\t\t// Min Distances between current centroids and data points\n\t\t\tlet dists = [], lk = ks.length;\n\t\t\tlet dsum = 0, prs = [];\n\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tlet min = Infinity;\n\t\t\t\tfor(let j=0;j<lk;j++) {\n\t\t\t\t\tlet dist = distance(data[i],ks[j]);\n\t\t\t\t\tif(dist<=min) min = dist;\n\t\t\t\t}\n\t\t\t\tdists[i] = min;\n\t\t\t}\n\n\t\t\t// Sum all min distances\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tdsum += dists[i]\n\t\t\t}\n\n\t\t\t// Probabilities and cummulative prob (cumsum)\n\t\t\tfor(let i=0;i<len;i++) {\n\t\t\t\tprs[i] = {i:i, v:data[i],\tpr:dists[i]/dsum, cs:0}\n\t\t\t}\n\n\t\t\t// Sort Probabilities\n\t\t\tprs.sort((a,b)=>a.pr-b.pr);\n\n\t\t\t// Cummulative Probabilities\n\t\t\tprs[0].cs = prs[0].pr;\n\t\t\tfor(let i=1;i<len;i++) {\n\t\t\t\tprs[i].cs = prs[i-1].cs + prs[i].pr;\n\t\t\t}\n\n\t\t\t// Randomize\n\t\t\tlet rnd = Math.random();\n\n\t\t\t// Gets only the items whose cumsum >= rnd\n\t\t\tlet idx = 0;\n\t\t\twhile(idx<len-1 && prs[idx++].cs<rnd);\n\t\t\tks.push(prs[idx-1].v);\n\t\t\t/*\n\t\t\tlet done = false;\n\t\t\twhile(!done) {\n\t\t\t\t// this is our new centroid\n\t\t\t\tc = prs[idx-1].v\n\t\t\t\tkey = multi? c.join(\"_\") : `${c}`;\n\t\t\t\tif(!map[key]) {\n\t\t\t\t\tmap[key] = true;\n\t\t\t\t\tks.push(c);\n\t\t\t\t\tdone = true;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tidx++;\n\t\t\t\t}\n\t\t\t}\n\t\t\t*/\n\t\t}\n\n\t\treturn ks;\n\t}\n\n}\n"]}