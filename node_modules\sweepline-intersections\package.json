{"name": "sweepline-intersections", "version": "1.5.0", "description": "A module to check if a polygon self-intersects using a sweepline algorithm", "main": "dist/sweeplineIntersections.js", "module": "dist/sweeplineIntersections.esm.js", "unpkg": "dist/sweeplineIntersections.min.js", "jsdelivr": "dist/sweeplineIntersections.min.js", "typings": "types.d.ts", "scripts": {"bench": "npm run build && node test/benchmark.js", "build": "rollup -c", "build:debug": "cross-env webpack --config debug/webpack.config.js --mode production", "debug": "cross-env webpack-dev-server --config debug/webpack.config.js --mode development --open --hot", "test": "ava --verbose"}, "files": ["dist/", "types.d.ts"], "ava": {"files": ["test/test.e2e.js", "test/*.spec.js"], "require": ["esm"]}, "author": "", "license": "MIT", "dependencies": {"tinyqueue": "^2.0.0"}, "devDependencies": {"@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-strip": "^1.3.2", "@types/geojson": "7946.0.8", "2d-polygon-self-intersections": "^1.3.1", "ava": "^1.0.1", "benchmark": "^2.1.4", "bentley-ottmann-intersections": "0.0.1", "cross-env": "^5.2.0", "css-loader": "^2.1.0", "eslint": "^5.12.0", "eslint-config-mourner": "^3.0.0", "esm": "^3.0.84", "file-loader": "^3.0.1", "geojson-polygon-self-intersections": "^1.2.0", "glob": "^7.1.3", "json-loader": "^0.5.7", "leaflet": "^1.4.0", "load-json-file": "^5.3.0", "nyc": "^13.1.0", "rollup": "^2.7.6", "rollup-plugin-terser": "^4.0.2", "vue": "^2.5.22", "vue-loader": "^15.6.2", "vue-template-compiler": "^2.5.22", "webpack": "^4.29.0", "webpack-cli": "^3.2.1", "webpack-dev-server": "^3.1.14"}, "repository": {"type": "git", "url": "git+https://github.com/rowanwins/sweepline-intersections.git"}, "bugs": {"url": "https://github.com/rowanwins/sweepline-intersections/issues"}, "keywords": ["kinks", "intersections", "self-intersections", "complex polygon"]}