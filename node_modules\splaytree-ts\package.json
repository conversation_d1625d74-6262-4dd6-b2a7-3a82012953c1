{"name": "splaytree-ts", "description": "A splay tree typescript library", "version": "1.0.2", "author": {"name": "<PERSON><PERSON>"}, "license": "BDS-3-<PERSON><PERSON>", "type": "module", "main": "dist/esm/index.js", "module": "dist/esm/index.js", "jsdelivr": "dist/splaytree-ts.umd.js", "unpkg": "dist/splaytree-ts.umd.js", "types": "dist/esm/index.d.ts", "exports": {"umd": "./dist/splaytree-ts.umd.js", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.cjs", "default": "./dist/cjs/index.cjs"}, "files": ["dist"], "keywords": ["splay", "tree", "map", "set", "self-balancing", "binary", "search"], "repository": {"type": "git", "url": "https://github.com/SBanksX/splaytree-ts.git"}, "scripts": {"preversion": "npm run test", "version": "npm run build && git add -A", "postversion": "git push && git push --tags", "build": "rollup -c && tsup", "test": "npm run test:typecheck && npm run test:lint", "test:lint": "eslint src", "test:typecheck": "tsc --noEmit"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.25.0", "@typescript-eslint/parser": "^5.25.0", "@babel/preset-env": "^7.19.0", "@babel/preset-typescript": "^7.18.6", "@rollup/plugin-json": "4", "@rollup/plugin-node-resolve": "13", "@rollup/plugin-typescript": "^8.5.0", "rollup-plugin-terser": "7", "eslint": "^8.16.0", "eslint-config-prettier": "^8.5.0", "tsup": "^8.3.5"}}