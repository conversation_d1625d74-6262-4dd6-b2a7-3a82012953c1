{"version": 3, "sources": ["../../browser.js"], "names": ["root", "previous_skmeans", "skmeans", "require", "exports", "module", "window", "call"], "mappings": "AAAA;;AAEA,CAAC,YAAW;AACV,MAAIA,OAAO,IAAX;AACA,MAAIC,mBAAmBD,KAAKE,OAA5B;AACD,MAAIA,UAAUC,QAAQ,WAAR,CAAd;;AAEA,MAAI,OAAOC,OAAP,KAAmB,WAAvB,EAAqC;AAClC,QAAI,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,OAAOD,OAA5C,EAAsD;AACpDA,gBAAUC,OAAOD,OAAP,GAAiBF,OAA3B;AACD;AACDE,YAAQF,OAAR,GAAkBA,OAAlB;AACD;;AAEF,MAAG,OAAOI,MAAP,KAAkB,WAArB,EAAkC;AAC/BA,WAAOJ,OAAP,GAAiBA,OAAjB;AACD;AAEF,CAhBD,EAgBGK,IAhBH", "file": "browser.js", "sourcesContent": ["\"use strict\";\n\n(function() {\n  var root = this\n  var previous_skmeans = root.skmeans;\n\tvar skmeans = require('./main.js');\n\n\tif( typeof exports !== 'undefined' ) {\n    if( typeof module !== 'undefined' && module.exports ) {\n      exports = module.exports = skmeans;\n    }\n    exports.skmeans = skmeans;\n  }\n\n\tif(typeof window !== 'undefined') {\n    window.skmeans = skmeans;\n  }\n\n}).call(this);\n"]}