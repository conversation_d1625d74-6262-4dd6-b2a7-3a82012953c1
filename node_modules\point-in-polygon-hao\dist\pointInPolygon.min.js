!function(r,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(r="undefined"!=typeof globalThis?globalThis:r||self).pointInPolygon=n()}(this,(function(){"use strict";var r=11102230246251565e-32,n=134217729,e=(3+8*r)*r;function t(r,n,e,t,f){var o,i,a,u,s=n[0],l=t[0],v=0,c=0;l>s==l>-s?(o=s,s=n[++v]):(o=l,l=t[++c]);var d=0;if(v<r&&c<e)for(l>s==l>-s?(a=o-((i=s+o)-s),s=n[++v]):(a=o-((i=l+o)-l),l=t[++c]),o=i,0!==a&&(f[d++]=a);v<r&&c<e;)l>s==l>-s?(a=o-((i=o+s)-(u=i-o))+(s-u),s=n[++v]):(a=o-((i=o+l)-(u=i-o))+(l-u),l=t[++c]),o=i,0!==a&&(f[d++]=a);for(;v<r;)a=o-((i=o+s)-(u=i-o))+(s-u),s=n[++v],o=i,0!==a&&(f[d++]=a);for(;c<e;)a=o-((i=o+l)-(u=i-o))+(l-u),l=t[++c],o=i,0!==a&&(f[d++]=a);return 0===o&&0!==d||(f[d++]=o),d}function f(r){return new Float64Array(r)}var o=f(4),i=f(8),a=f(12),u=f(16),s=f(4);function l(r,f,l,v,c,d){var h=(f-d)*(l-c),b=(r-c)*(v-d),p=h-b,g=Math.abs(h+b);return Math.abs(p)>=33306690738754716e-32*g?p:-function(r,f,l,v,c,d,h){var b,p,g,y,m,w,M,x,F,T,j,A,E,I,P,k,q,z,B=r-c,C=l-c,D=f-d,G=v-d;m=(P=(x=B-(M=(w=n*B)-(w-B)))*(T=G-(F=(w=n*G)-(w-G)))-((I=B*G)-M*F-x*F-M*T))-(j=P-(q=(x=D-(M=(w=n*D)-(w-D)))*(T=C-(F=(w=n*C)-(w-C)))-((k=D*C)-M*F-x*F-M*T))),o[0]=P-(j+m)+(m-q),m=(E=I-((A=I+j)-(m=A-I))+(j-m))-(j=E-k),o[1]=E-(j+m)+(m-k),m=(z=A+j)-A,o[2]=A-(z-m)+(j-m),o[3]=z;var H=function(r,n){for(var e=n[0],t=1;t<r;t++)e+=n[t];return e}(4,o),J=22204460492503146e-32*h;if(H>=J||-H>=J)return H;if(b=r-(B+(m=r-B))+(m-c),g=l-(C+(m=l-C))+(m-c),p=f-(D+(m=f-D))+(m-d),y=v-(G+(m=v-G))+(m-d),0===b&&0===p&&0===g&&0===y)return H;if(J=11093356479670487e-47*h+e*Math.abs(H),(H+=B*y+G*b-(D*g+C*p))>=J||-H>=J)return H;m=(P=(x=b-(M=(w=n*b)-(w-b)))*(T=G-(F=(w=n*G)-(w-G)))-((I=b*G)-M*F-x*F-M*T))-(j=P-(q=(x=p-(M=(w=n*p)-(w-p)))*(T=C-(F=(w=n*C)-(w-C)))-((k=p*C)-M*F-x*F-M*T))),s[0]=P-(j+m)+(m-q),m=(E=I-((A=I+j)-(m=A-I))+(j-m))-(j=E-k),s[1]=E-(j+m)+(m-k),m=(z=A+j)-A,s[2]=A-(z-m)+(j-m),s[3]=z;var K=t(4,o,4,s,i);m=(P=(x=B-(M=(w=n*B)-(w-B)))*(T=y-(F=(w=n*y)-(w-y)))-((I=B*y)-M*F-x*F-M*T))-(j=P-(q=(x=D-(M=(w=n*D)-(w-D)))*(T=g-(F=(w=n*g)-(w-g)))-((k=D*g)-M*F-x*F-M*T))),s[0]=P-(j+m)+(m-q),m=(E=I-((A=I+j)-(m=A-I))+(j-m))-(j=E-k),s[1]=E-(j+m)+(m-k),m=(z=A+j)-A,s[2]=A-(z-m)+(j-m),s[3]=z;var L=t(K,i,4,s,a);m=(P=(x=b-(M=(w=n*b)-(w-b)))*(T=y-(F=(w=n*y)-(w-y)))-((I=b*y)-M*F-x*F-M*T))-(j=P-(q=(x=p-(M=(w=n*p)-(w-p)))*(T=g-(F=(w=n*g)-(w-g)))-((k=p*g)-M*F-x*F-M*T))),s[0]=P-(j+m)+(m-q),m=(E=I-((A=I+j)-(m=A-I))+(j-m))-(j=E-k),s[1]=E-(j+m)+(m-k),m=(z=A+j)-A,s[2]=A-(z-m)+(j-m),s[3]=z;var N=t(L,a,4,s,u);return u[N-1]}(r,f,l,v,c,d,g)}return function(r,n){var e,t,f,o,i,a,u,s,v,c=0,d=r[0],h=r[1],b=n.length;for(e=0;e<b;e++){t=0;var p=n[e],g=p.length-1;if((s=p[0])[0]!==p[g][0]&&s[1]!==p[g][1])throw new Error("First and last coordinates in a ring must be the same");for(o=s[0]-d,i=s[1]-h;t<g;t++){if(a=(v=p[t+1])[0]-d,u=v[1]-h,0===i&&0===u){if(a<=0&&o>=0||o<=0&&a>=0)return 0}else if(u>=0&&i<=0||u<=0&&i>=0){if(0===(f=l(o,a,i,u,0,0)))return 0;(f>0&&u>0&&i<=0||f<0&&u<=0&&i>0)&&c++}s=v,i=u,o=a}}return c%2!=0}}));
