!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t=t||self).TinyQueue=i()}(this,function(){"use strict";var t=function(t,e){if(void 0===t&&(t=[]),void 0===e&&(e=i),this.data=t,this.length=this.data.length,this.compare=e,this.length>0)for(var n=(this.length>>1)-1;n>=0;n--)this._down(n)};function i(t,i){return t<i?-1:t>i?1:0}return t.prototype.push=function(t){this.data.push(t),this.length++,this._up(this.length-1)},t.prototype.pop=function(){if(0!==this.length){var t=this.data[0],i=this.data.pop();return this.length--,this.length>0&&(this.data[0]=i,this._down(0)),t}},t.prototype.peek=function(){return this.data[0]},t.prototype._up=function(t){for(var i=this.data,e=this.compare,n=i[t];t>0;){var o=t-1>>1,h=i[o];if(e(n,h)>=0)break;i[t]=h,t=o}i[t]=n},t.prototype._down=function(t){for(var i=this.data,e=this.compare,n=this.length>>1,o=i[t];t<n;){var h=1+(t<<1),s=i[h],a=h+1;if(a<this.length&&e(i[a],s)<0&&(h=a,s=i[a]),e(s,o)>=0)break;i[t]=s,t=h}i[t]=o},t});
