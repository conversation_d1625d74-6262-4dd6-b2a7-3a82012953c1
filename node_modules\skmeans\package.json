{"name": "skmeans", "version": "0.9.7", "description": "Super fast simple k-means and k-means++ clustering for unidimiensional and multidimensional data. Works in node and browser", "author": "<PERSON> <<EMAIL>>", "url": "https://github.com/owner/project/issues", "email": "<EMAIL>", "main": "dist/node/main.js", "license": "MIT", "repository": "solzimer/skmeans", "keywords": ["k-means", "k-means++", "kmeans++", "kmeans", "simple", "cluster", "fast", "unidimiensional", "multidimensional"], "dependencies": {}, "devDependencies": {"babel-preset-es2015": "^6.24.1", "grunt": "^1.0.1", "grunt-babel": "^6.0.0", "grunt-browserify": "^5.0.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-uglify": "^3.0.1", "load-grunt-tasks": "^3.5.2", "uglify": "^0.1.5"}}