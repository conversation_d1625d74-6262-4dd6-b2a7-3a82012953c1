{"name": "point-in-polygon-hao", "version": "1.2.4", "type": "module", "description": "A point in polygon based on the paper Optimal Reliable Point-in-Polygon Test and Differential Coding Boolean Operations on Polygons", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/pointInPolygon.min.js", "jsdelivr": "dist/pointInPolygon.min.js", "scripts": {"bench": "npm run build && node test/bench.js", "build": "rollup -c", "test": "vitest"}, "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.cjs"}}, "files": ["dist"], "dependencies": {"robust-predicates": "^3.0.2"}, "devDependencies": {"@rollup/plugin-buble": "^1.0.3", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "benchmark": "^2.1.4", "eslint": "^5.15.3", "eslint-config-mourner": "^3.0.0", "esm": "^3.2.20", "load-json-file": "^5.2.0", "point-in-polygon": "^1.0.1", "robust-point-in-polygon": "^1.0.3", "rollup": "^4.28.0", "rollup-plugin-copy": "^3.5.0", "vitest": "^2.1.6"}, "keywords": ["point-in-polygon", "pip", "point", "polygon", "inside"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/rowanwins/point-in-polygon-hao.git"}, "homepage": "https://github.com/rowanwins/point-in-polygon-hao"}